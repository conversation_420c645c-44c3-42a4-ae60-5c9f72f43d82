<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 32 32" width="32" height="32">
  <defs>
    <linearGradient id="grad1" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#165DFF;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#10B981;stop-opacity:1" />
    </linearGradient>
  </defs>
  
  <!-- 背景圆形 -->
  <circle cx="16" cy="16" r="15" fill="url(#grad1)" stroke="#ffffff" stroke-width="1"/>
  
  <!-- 播放按钮三角形 -->
  <polygon points="12,10 12,22 22,16" fill="#ffffff" stroke="#ffffff" stroke-width="0.5"/>
  
  <!-- 装饰性圆点 -->
  <circle cx="8" cy="8" r="1.5" fill="#ffffff" opacity="0.8"/>
  <circle cx="24" cy="8" r="1.5" fill="#ffffff" opacity="0.8"/>
  <circle cx="8" cy="24" r="1.5" fill="#ffffff" opacity="0.8"/>
  <circle cx="24" cy="24" r="1.5" fill="#ffffff" opacity="0.8"/>
</svg>
