<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Favicon Generator</title>
</head>
<body>
    <h2>Favicon Generator</h2>
    <canvas id="canvas" width="32" height="32" style="border: 1px solid #ccc; image-rendering: pixelated; width: 128px; height: 128px;"></canvas>
    <br><br>
    <button onclick="downloadFavicon()">下载 favicon.png</button>
    <button onclick="downloadIco()">下载 favicon.ico</button>

    <script>
        const canvas = document.getElementById('canvas');
        const ctx = canvas.getContext('2d');

        // 创建渐变背景
        const gradient = ctx.createLinearGradient(0, 0, 32, 32);
        gradient.addColorStop(0, '#165DFF');
        gradient.addColorStop(1, '#10B981');

        // 绘制背景圆形
        ctx.fillStyle = gradient;
        ctx.beginPath();
        ctx.arc(16, 16, 15, 0, 2 * Math.PI);
        ctx.fill();

        // 绘制白色边框
        ctx.strokeStyle = '#ffffff';
        ctx.lineWidth = 1;
        ctx.stroke();

        // 绘制播放按钮三角形
        ctx.fillStyle = '#ffffff';
        ctx.beginPath();
        ctx.moveTo(12, 10);
        ctx.lineTo(12, 22);
        ctx.lineTo(22, 16);
        ctx.closePath();
        ctx.fill();

        // 绘制装饰性圆点
        ctx.fillStyle = 'rgba(255, 255, 255, 0.8)';
        
        // 四个角的圆点
        ctx.beginPath();
        ctx.arc(8, 8, 1.5, 0, 2 * Math.PI);
        ctx.fill();
        
        ctx.beginPath();
        ctx.arc(24, 8, 1.5, 0, 2 * Math.PI);
        ctx.fill();
        
        ctx.beginPath();
        ctx.arc(8, 24, 1.5, 0, 2 * Math.PI);
        ctx.fill();
        
        ctx.beginPath();
        ctx.arc(24, 24, 1.5, 0, 2 * Math.PI);
        ctx.fill();

        function downloadFavicon() {
            const link = document.createElement('a');
            link.download = 'favicon.png';
            link.href = canvas.toDataURL('image/png');
            link.click();
        }

        function downloadIco() {
            // 创建16x16版本
            const canvas16 = document.createElement('canvas');
            canvas16.width = 16;
            canvas16.height = 16;
            const ctx16 = canvas16.getContext('2d');
            ctx16.drawImage(canvas, 0, 0, 32, 32, 0, 0, 16, 16);

            // 简单的ICO格式（实际项目中建议使用专门的库）
            const link = document.createElement('a');
            link.download = 'favicon-16x16.png';
            link.href = canvas16.toDataURL('image/png');
            link.click();
        }
    </script>
</body>
</html>
