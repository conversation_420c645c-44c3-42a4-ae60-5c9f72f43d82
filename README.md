# 🎬 免费影视导航

一个现代化的免费影视站点导航页面，提供多个影视平台的访问链接，支持实时状态检测和暗黑模式。

## ✨ 功能特性

### 🎨 界面设计
- **现代化UI设计**：采用渐变背景和卡片式布局
- **暗黑模式支持**：三种主题模式（跟随系统、白天模式、黑夜模式）
- **响应式设计**：完美适配桌面端、平板和移动设备
- **可爱风格动画**：加载动画和交互效果

### 🔍 智能检测
- **实时状态检测**：自动检测各个影视站点的可用性
- **多重检测方法**：使用多种技术确保检测准确性
- **响应时间显示**：显示各站点的访问速度
- **自动重试机制**：提高检测成功率

### 📊 数据统计
- **实时统计面板**：显示总线路数、在线线路、离线线路和成功率
- **最后检查时间**：记录最近一次状态检查的时间
- **状态可视化**：用颜色标识不同的线路状态

### 🛠️ 用户体验
- **一键刷新**：快速更新所有线路状态
- **单独检测**：可以单独检查某个线路的状态
- **自动检查**：定时自动检查线路状态（30分钟间隔）
- **通知提示**：操作完成后的友好提示

## 🚀 快速开始

### 环境要求
- 现代浏览器（Chrome、Firefox、Safari、Edge等）
- 支持ES6+的JavaScript环境
- 网络连接（用于加载外部资源和检测站点状态）

### 安装使用

1. **克隆项目**
   ```bash
   git clone https://github.com/your-username/free-video.git
   cd free-video
   ```

2. **直接使用**
   ```bash
   # 使用任意HTTP服务器运行
   # 例如使用Python
   python -m http.server 8000
   
   # 或使用Node.js
   npx serve .
   
   # 然后在浏览器中访问 http://localhost:8000
   ```

3. **或者直接打开**
   ```bash
   # 直接在浏览器中打开index.html文件
   open index.html  # macOS
   start index.html # Windows
   ```

## 📁 项目结构

```
free-video/
├── index.html              # 主HTML文件
├── styles.css              # 样式文件
├── script.js               # JavaScript主文件
├── sites.json              # 站点配置文件
├── favicon.svg             # SVG格式图标
├── favicon-16x16.png       # 16x16 PNG图标
├── favicon-32x32.png       # 32x32 PNG图标
├── apple-touch-icon.png    # 180x180 Apple图标
├── site.webmanifest        # PWA清单文件
├── generate-favicon.html   # 图标生成工具
├── generate-apple-icon.html # Apple图标生成工具
├── create-favicons.js      # Node.js图标生成脚本
├── index_backup.html       # 原始单文件备份
├── README.md               # 项目说明文档
├── LICENSE                 # 开源协议
└── .gitignore              # Git忽略文件
```

### 文件说明

- **index.html**：主页面文件，包含页面结构和布局
- **styles.css**：所有样式定义，包括响应式设计和动画效果
- **script.js**：主要功能逻辑，包括主题管理、状态检测等
- **sites.json**：站点配置文件，可以轻松添加或修改站点信息
- **favicon.svg**：可缩放矢量图标，支持现代浏览器
- **favicon-16x16.png / favicon-32x32.png**：不同尺寸的PNG图标
- **apple-touch-icon.png**：Apple设备专用的180x180图标
- **site.webmanifest**：PWA应用清单文件，支持添加到主屏幕
- **generate-favicon.html / generate-apple-icon.html**：图标生成工具

## ⚙️ 配置说明

### 添加新站点

编辑 `sites.json` 文件，按以下格式添加新站点：

```json
{
  "sites": [
    {
      "name": "站点名称",
      "icon": "fa-图标名称",
      "links": [
        {
          "url": "https://example.com",
          "platform": "平台名称",
          "responseTime": null,
          "status": "unknown"
        }
      ]
    }
  ]
}
```

### 自定义主题

在 `styles.css` 中修改CSS变量来自定义主题颜色：

```css
:root {
  --primary: #165DFF;    /* 主色调 */
  --success: #10B981;    /* 成功色 */
  --warning: #F59E0B;    /* 警告色 */
  --danger: #EF4444;     /* 危险色 */
}
```

## 🎯 核心功能

### 主题切换
- **跟随系统**：自动跟随操作系统的主题设置
- **白天模式**：明亮的界面主题
- **黑夜模式**：深色的界面主题

### 状态检测
- **在线**：站点可正常访问（绿色标识）
- **离线**：站点无法访问（红色标识）
- **超时**：检测超时（黄色标识）
- **未检查**：尚未进行状态检测（灰色标识）

### 响应式设计
- **桌面端**：完整功能显示，最佳用户体验
- **平板端**：适中的布局和字体大小
- **移动端**：紧凑的界面，隐藏非必要文字

## 🔧 技术栈

- **前端框架**：原生JavaScript (ES6+)
- **CSS框架**：Tailwind CSS
- **图标库**：Font Awesome 4.7.0
- **字体**：Google Fonts (Inter)
- **构建工具**：无需构建，直接运行

## 📱 浏览器支持

| 浏览器 | 版本要求 |
|--------|----------|
| Chrome | 60+ |
| Firefox | 55+ |
| Safari | 12+ |
| Edge | 79+ |

## 🤝 贡献指南

欢迎提交Issue和Pull Request！

1. Fork 本项目
2. 创建特性分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 开启 Pull Request

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情

## 🙏 致谢

- [Tailwind CSS](https://tailwindcss.com/) - 优秀的CSS框架
- [Font Awesome](https://fontawesome.com/) - 丰富的图标库
- [Google Fonts](https://fonts.google.com/) - 优质的网络字体

## 📞 联系方式

如有问题或建议，请通过以下方式联系：

- 提交 [Issue](https://github.com/your-username/free-video/issues)
- 发送邮件至：<EMAIL>

## 🔍 详细功能说明

### 主题管理系统
项目实现了完整的主题管理系统，支持：
- **本地存储**：主题选择会保存到浏览器本地存储
- **系统监听**：自动监听系统主题变化
- **平滑过渡**：主题切换时的平滑动画效果

### 链接状态检测机制
采用多重检测策略确保准确性：
1. **HEAD请求检测**：首先尝试HEAD请求
2. **GET请求检测**：HEAD失败时尝试GET请求
3. **图片加载检测**：最后尝试加载favicon图标
4. **超时控制**：每种方法都有10秒超时限制
5. **重试机制**：失败时自动重试最多2次

### 性能优化
- **并发控制**：限制同时检测的链接数量（最多3个）
- **缓存机制**：浏览器缓存CSS和JS文件
- **懒加载**：按需加载和渲染内容
- **防抖处理**：避免频繁的状态更新

## 📋 开发指南

### 本地开发
```bash
# 1. 克隆项目
git clone https://github.com/your-username/free-video.git
cd free-video

# 2. 启动本地服务器（推荐）
# 使用Python 3
python -m http.server 8000

# 使用Python 2
python -m SimpleHTTPServer 8000

# 使用Node.js
npx serve . -p 8000

# 使用PHP
php -S localhost:8000

# 3. 访问应用
# 打开浏览器访问 http://localhost:8000
```

### 代码规范
- **JavaScript**：使用ES6+语法，采用类和模块化设计
- **CSS**：使用Tailwind CSS工具类，自定义样式采用BEM命名
- **HTML**：语义化标签，无障碍访问支持
- **JSON**：站点配置使用标准JSON格式

### 调试技巧
1. **开发者工具**：使用浏览器开发者工具查看网络请求
2. **控制台日志**：检查控制台输出的状态检测日志
3. **网络面板**：监控API请求和资源加载
4. **移动端调试**：使用设备模拟器测试响应式设计

## 🚨 常见问题

### Q: 为什么某些站点显示离线但实际可以访问？
A: 这可能是由于以下原因：
- 站点设置了CORS策略，阻止跨域请求
- 站点使用了防爬虫机制
- 网络环境限制（如公司防火墙）
- 站点临时维护或不稳定

### Q: 如何添加新的影视站点？
A: 编辑`sites.json`文件，按照现有格式添加新站点信息：
```json
{
  "name": "新站点名称",
  "icon": "fa-television",
  "links": [
    {
      "url": "https://newsite.com",
      "platform": "主线路",
      "responseTime": null,
      "status": "unknown"
    }
  ]
}
```

### Q: 如何自定义检测间隔时间？
A: 在`script.js`文件中找到`startAutoCheck(30)`，将30改为你想要的分钟数。

### Q: 移动端显示异常怎么办？
A: 确保：
- 浏览器支持CSS Grid和Flexbox
- JavaScript已启用
- 网络连接正常
- 清除浏览器缓存后重试

## 🔄 更新日志

### v2.0.0 (最新版本)
- ✨ 重构为模块化文件结构
- ✨ 新增站点配置JSON文件
- ✨ 优化主题切换体验
- ✨ 改进移动端适配
- 🐛 修复状态检测偶尔失效的问题
- 🐛 修复暗黑模式下的样式问题

### v1.0.0
- 🎉 初始版本发布
- ✨ 基础的站点导航功能
- ✨ 实时状态检测
- ✨ 响应式设计
- ✨ 暗黑模式支持

## 🛣️ 未来计划

- [ ] PWA支持（离线访问）
- [ ] 站点收藏功能
- [ ] 历史访问记录
- [ ] 更多主题选择
- [ ] 国际化支持
- [ ] 站点分类管理
- [ ] 用户自定义配置
- [ ] 数据导入导出

---

⭐ 如果这个项目对你有帮助，请给它一个星标！

💡 有任何建议或想法，欢迎提交Issue讨论！
