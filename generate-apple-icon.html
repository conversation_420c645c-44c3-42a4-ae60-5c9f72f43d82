<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Apple Touch Icon Generator</title>
</head>
<body>
    <h2>Apple Touch Icon Generator (180x180)</h2>
    <canvas id="canvas" width="180" height="180" style="border: 1px solid #ccc; width: 180px; height: 180px;"></canvas>
    <br><br>
    <button onclick="downloadIcon()">下载 apple-touch-icon.png</button>

    <script>
        const canvas = document.getElementById('canvas');
        const ctx = canvas.getContext('2d');
        const size = 180;

        // 创建渐变背景
        const gradient = ctx.createLinearGradient(0, 0, size, size);
        gradient.addColorStop(0, '#165DFF');
        gradient.addColorStop(1, '#10B981');

        // 绘制背景圆形
        ctx.fillStyle = gradient;
        ctx.beginPath();
        ctx.arc(size/2, size/2, size/2 - 5, 0, 2 * Math.PI);
        ctx.fill();

        // 绘制白色边框
        ctx.strokeStyle = '#ffffff';
        ctx.lineWidth = 4;
        ctx.stroke();

        // 绘制播放按钮三角形
        ctx.fillStyle = '#ffffff';
        ctx.beginPath();
        const centerX = size/2;
        const centerY = size/2;
        const triangleSize = size * 0.3;
        
        ctx.moveTo(centerX - triangleSize/2, centerY - triangleSize/2);
        ctx.lineTo(centerX - triangleSize/2, centerY + triangleSize/2);
        ctx.lineTo(centerX + triangleSize/2, centerY);
        ctx.closePath();
        ctx.fill();

        // 绘制装饰性圆点
        ctx.fillStyle = 'rgba(255, 255, 255, 0.8)';
        const dotSize = size * 0.04;
        const dotOffset = size * 0.25;
        
        // 四个角的圆点
        [[dotOffset, dotOffset], [size - dotOffset, dotOffset], 
         [dotOffset, size - dotOffset], [size - dotOffset, size - dotOffset]].forEach(([x, y]) => {
            ctx.beginPath();
            ctx.arc(x, y, dotSize, 0, 2 * Math.PI);
            ctx.fill();
        });

        // 添加一些额外的装饰元素
        ctx.fillStyle = 'rgba(255, 255, 255, 0.6)';
        
        // 中心周围的小圆点
        for (let i = 0; i < 8; i++) {
            const angle = (i * Math.PI * 2) / 8;
            const radius = size * 0.35;
            const x = centerX + Math.cos(angle) * radius;
            const y = centerY + Math.sin(angle) * radius;
            
            ctx.beginPath();
            ctx.arc(x, y, dotSize * 0.6, 0, 2 * Math.PI);
            ctx.fill();
        }

        function downloadIcon() {
            const link = document.createElement('a');
            link.download = 'apple-touch-icon.png';
            link.href = canvas.toDataURL('image/png');
            link.click();
        }
    </script>
</body>
</html>
