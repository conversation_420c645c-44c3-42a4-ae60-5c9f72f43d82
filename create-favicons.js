// Node.js script to generate favicon files
// 需要安装: npm install canvas

const { createCanvas } = require('canvas');
const fs = require('fs');

function createFavicon(size, filename) {
    const canvas = createCanvas(size, size);
    const ctx = canvas.getContext('2d');

    // 创建渐变背景
    const gradient = ctx.createLinearGradient(0, 0, size, size);
    gradient.addColorStop(0, '#165DFF');
    gradient.addColorStop(1, '#10B981');

    // 绘制背景圆形
    ctx.fillStyle = gradient;
    ctx.beginPath();
    ctx.arc(size/2, size/2, size/2 - 1, 0, 2 * Math.PI);
    ctx.fill();

    // 绘制白色边框
    ctx.strokeStyle = '#ffffff';
    ctx.lineWidth = size > 16 ? 2 : 1;
    ctx.stroke();

    // 绘制播放按钮三角形
    ctx.fillStyle = '#ffffff';
    ctx.beginPath();
    const centerX = size/2;
    const centerY = size/2;
    const triangleSize = size * 0.3;
    
    ctx.moveTo(centerX - triangleSize/2, centerY - triangleSize/2);
    ctx.lineTo(centerX - triangleSize/2, centerY + triangleSize/2);
    ctx.lineTo(centerX + triangleSize/2, centerY);
    ctx.closePath();
    ctx.fill();

    // 绘制装饰性圆点（仅在较大尺寸时）
    if (size >= 32) {
        ctx.fillStyle = 'rgba(255, 255, 255, 0.8)';
        const dotSize = size * 0.05;
        const dotOffset = size * 0.25;
        
        // 四个角的圆点
        [[dotOffset, dotOffset], [size - dotOffset, dotOffset], 
         [dotOffset, size - dotOffset], [size - dotOffset, size - dotOffset]].forEach(([x, y]) => {
            ctx.beginPath();
            ctx.arc(x, y, dotSize, 0, 2 * Math.PI);
            ctx.fill();
        });
    }

    // 保存文件
    const buffer = canvas.toBuffer('image/png');
    fs.writeFileSync(filename, buffer);
    console.log(`Created ${filename} (${size}x${size})`);
}

// 生成不同尺寸的favicon
createFavicon(16, 'favicon-16x16.png');
createFavicon(32, 'favicon-32x32.png');
createFavicon(180, 'apple-touch-icon.png');

console.log('All favicon files created successfully!');
console.log('To use this script, run: npm install canvas && node create-favicons.js');
