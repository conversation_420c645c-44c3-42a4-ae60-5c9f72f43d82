{"sites": [{"name": "LibreTV", "icon": "fa-television", "links": [{"url": "https://libretv-jsh40svv.edgeone.app/", "platform": "EdgeOne", "responseTime": null, "status": "unknown"}, {"url": "https://libretv-3fh.pages.dev/", "platform": "Cloudflare", "responseTime": null, "status": "unknown"}, {"url": "https://libretv.magiccode.dpdns.org/", "platform": "MagicCode", "responseTime": null, "status": "unknown"}]}, {"name": "MoonTV", "icon": "fa-moon-o", "links": [{"url": "https://moontv-bnx.pages.dev/", "platform": "Cloudflare", "responseTime": null, "status": "unknown"}, {"url": "https://moontv.magiccode.dpdns.org/", "platform": "MagicCode", "responseTime": null, "status": "unknown"}]}, {"name": "网盘资源", "icon": "fa-cloud-download", "links": [{"url": "https://www.aipan.me/", "platform": "爱盼", "responseTime": null, "status": "unknown"}, {"url": "https://cloudsaver-test.160621.xyz/resource", "platform": "云转存", "responseTime": null, "status": "unknown"}]}]}