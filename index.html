<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>免费影视站点导航</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://cdn.jsdelivr.net/npm/font-awesome@4.7.0/css/font-awesome.min.css" rel="stylesheet">

    <!-- 配置Tailwind自定义颜色和字体 -->
    <script>
        tailwind.config = {
            darkMode: 'class', // 启用class模式的暗黑主题
            theme: {
                extend: {
                    colors: {
                        primary: '#165DFF',
                        success: '#10B981',
                        warning: '#F59E0B',
                        danger: '#EF4444',
                        dark: '#1E293B',
                        light: '#F8FAFC'
                    },
                    fontFamily: {
                        inter: ['Inter', 'system-ui', 'sans-serif'],
                    },
                },
            }
        }
    </script>

    <style type="text/tailwindcss">
        @layer utilities {
      .card-shadow {
        box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
      }
      .dark .card-shadow {
        box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.3), 0 4px 6px -2px rgba(0, 0, 0, 0.2);
      }
      .card-hover {
        transition: all 0.3s ease;
      }
      .card-hover:hover {
        transform: translateY(-5px);
        box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
      }
      .dark .card-hover:hover {
        box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.4), 0 10px 10px -5px rgba(0, 0, 0, 0.3);
      }
      .status-pulse {
        animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
      }
      @keyframes pulse {
        0%, 100% {
          opacity: 1;
        }
        50% {
          opacity: 0.7;
        }
      }
      .link-item {
        cursor: pointer;
        user-select: none;
      }
      .link-item:hover {
        transform: translateY(-1px);
      }
      .link-item:active {
        transform: translateY(0);
      }

      /* 主题切换按钮样式 */
      .theme-btn {
        color: #6b7280;
      }
      .theme-btn.active {
        background-color: #165DFF;
        color: white;
      }
      .dark .theme-btn {
        color: #9ca3af;
      }
      .dark .theme-btn.active {
        background-color: #165DFF;
        color: white;
      }
    }
  </style>
</head>

<body class="bg-gradient-to-br from-gray-50 to-gray-100 dark:from-gray-900 dark:to-gray-800 min-h-screen font-inter text-dark dark:text-gray-100 transition-colors duration-300">
    <!-- 顶部导航 -->
    <header class="bg-white dark:bg-gray-800 shadow-md sticky top-0 z-50 transition-all duration-300">
        <div class="container mx-auto px-4 py-4 flex justify-between items-center">
            <div class="flex items-center space-x-2">
                <i class="fa fa-play-circle text-primary text-2xl"></i>
                <h1 class="text-[clamp(1.25rem,3vw,1.75rem)] font-bold text-primary">免费影视导航</h1>
            </div>
            <div class="flex items-center space-x-4">
                <!-- 主题切换按钮组 -->
                <div class="flex items-center bg-gray-100 dark:bg-gray-700 rounded-lg p-1 transition-colors duration-300">
                    <button id="theme-system" class="theme-btn px-3 py-1.5 text-xs font-medium rounded-md transition-all duration-200 flex items-center space-x-1" title="跟随系统">
                        <i class="fa fa-desktop"></i>
                        <span class="hidden sm:inline">系统</span>
                    </button>
                    <button id="theme-light" class="theme-btn px-3 py-1.5 text-xs font-medium rounded-md transition-all duration-200 flex items-center space-x-1" title="白天模式">
                        <i class="fa fa-sun-o"></i>
                        <span class="hidden sm:inline">白天</span>
                    </button>
                    <button id="theme-dark" class="theme-btn px-3 py-1.5 text-xs font-medium rounded-md transition-all duration-200 flex items-center space-x-1" title="黑夜模式">
                        <i class="fa fa-moon-o"></i>
                        <span class="hidden sm:inline">黑夜</span>
                    </button>
                </div>

                <span id="last-checked" class="text-sm text-gray-500 dark:text-gray-400 hidden md:inline-block">
                    <i class="fa fa-refresh mr-1"></i>
                    <span>最后检查: 从未</span>
                </span>
                <button id="refresh-btn"
                    class="bg-primary hover:bg-primary/90 text-white px-4 py-2 rounded-lg flex items-center transition-all">
                    <i class="fa fa-refresh mr-2"></i>
                    <span>刷新状态</span>
                </button>
            </div>
        </div>
    </header>

    <!-- 主内容区 -->
    <main class="container mx-auto px-4 py-8">
        <!-- 介绍部分 -->
        <div class="text-center mb-10 max-w-3xl mx-auto">
            <p class="text-gray-600 dark:text-gray-300 text-lg">以下是可用的免费影视站点，每个站点提供多条访问线路。绿色标识表示线路可正常访问，红色表示线路暂时不可用。</p>
        </div>

        <!-- 状态统计面板 -->
        <div id="status-panel" class="bg-white dark:bg-gray-800 rounded-xl shadow-lg p-6 mb-8 max-w-4xl mx-auto transition-colors duration-300">
            <h3 class="text-lg font-bold mb-4 flex items-center text-gray-900 dark:text-gray-100">
                <i class="fa fa-bar-chart text-primary mr-2"></i>线路状态统计
            </h3>
            <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
                <div class="text-center p-4 bg-gray-50 dark:bg-gray-700 rounded-lg transition-colors duration-300">
                    <div class="text-2xl font-bold text-gray-700 dark:text-gray-200" id="total-links">-</div>
                    <div class="text-sm text-gray-500 dark:text-gray-400">总线路数</div>
                </div>
                <div class="text-center p-4 bg-success/10 dark:bg-success/20 rounded-lg transition-colors duration-300">
                    <div class="text-2xl font-bold text-success" id="online-links">-</div>
                    <div class="text-sm text-success">在线线路</div>
                </div>
                <div class="text-center p-4 bg-danger/10 dark:bg-danger/20 rounded-lg transition-colors duration-300">
                    <div class="text-2xl font-bold text-danger" id="offline-links">-</div>
                    <div class="text-sm text-danger">离线线路</div>
                </div>
                <div class="text-center p-4 bg-primary/10 dark:bg-primary/20 rounded-lg transition-colors duration-300">
                    <div class="text-2xl font-bold text-primary" id="success-rate">-</div>
                    <div class="text-sm text-primary">成功率</div>
                </div>
            </div>
        </div>

        <!-- 站点卡片容器 -->
        <div id="sites-container" class="grid grid-cols-1 md:grid-cols-2 gap-8 mb-12">
            <!-- 站点将通过JavaScript动态生成 -->
        </div>

        <!-- 使用说明 -->
        <div class="bg-white dark:bg-gray-800 rounded-xl shadow-md p-6 max-w-3xl mx-auto transition-colors duration-300">
            <h3 class="text-xl font-bold mb-4 flex items-center text-gray-900 dark:text-gray-100">
                <i class="fa fa-info-circle text-primary mr-2"></i>使用说明
            </h3>
            <ul class="space-y-3 text-gray-600 dark:text-gray-300">
                <li class="flex items-start">
                    <i class="fa fa-check-circle text-success mt-1 mr-3"></i>
                    <span>点击线路链接将在新窗口打开对应网站</span>
                </li>
                <li class="flex items-start">
                    <i class="fa fa-check-circle text-success mt-1 mr-3"></i>
                    <span>如果某条线路无法访问，请尝试其他线路</span>
                </li>
                <li class="flex items-start">
                    <i class="fa fa-check-circle text-success mt-1 mr-3"></i>
                    <span>点击"刷新状态"按钮可以重新检查所有线路的可用性</span>
                </li>
            </ul>
        </div>
    </main>

    <!-- 页脚 -->
    <footer class="mt-16 py-8">
        <div class="container mx-auto px-4 text-center">
            <p class="mb-4 text-gray-600 dark:text-gray-300">免费影视站点导航</p>
            <p class="text-gray-500 dark:text-gray-400 text-sm">© 2025 免费影视站点导航页面 | 所有链接均搜集自互联网</p>
        </div>
    </footer>

    <!-- 通知提示 -->
    <div id="notification"
        class="fixed bottom-6 right-6 bg-dark dark:bg-gray-700 text-white px-6 py-3 rounded-lg shadow-lg transform translate-y-20 opacity-0 transition-all duration-300 flex items-center">
        <i class="fa fa-check-circle mr-2"></i>
        <span id="notification-text">操作成功</span>
    </div>

    <script>
        // 主题管理功能
        class ThemeManager {
            constructor() {
                this.themes = {
                    system: 'system',
                    light: 'light',
                    dark: 'dark'
                };
                this.currentTheme = this.getStoredTheme() || this.themes.system;
                this.init();
            }

            init() {
                this.applyTheme(this.currentTheme);
                this.setupEventListeners();
                this.updateButtonStates();

                // 监听系统主题变化
                if (window.matchMedia) {
                    window.matchMedia('(prefers-color-scheme: dark)').addEventListener('change', () => {
                        if (this.currentTheme === this.themes.system) {
                            this.applySystemTheme();
                        }
                    });
                }
            }

            getStoredTheme() {
                try {
                    return localStorage.getItem('theme');
                } catch (e) {
                    console.warn('无法访问localStorage，使用默认主题');
                    return null;
                }
            }

            setStoredTheme(theme) {
                try {
                    localStorage.setItem('theme', theme);
                } catch (e) {
                    console.warn('无法保存主题设置到localStorage');
                }
            }

            getSystemTheme() {
                if (window.matchMedia && window.matchMedia('(prefers-color-scheme: dark)').matches) {
                    return 'dark';
                }
                return 'light';
            }

            applySystemTheme() {
                const systemTheme = this.getSystemTheme();
                this.applyThemeToDOM(systemTheme);
            }

            applyThemeToDOM(theme) {
                const html = document.documentElement;

                if (theme === 'dark') {
                    html.classList.add('dark');
                } else {
                    html.classList.remove('dark');
                }
            }

            applyTheme(theme) {
                this.currentTheme = theme;
                this.setStoredTheme(theme);

                if (theme === this.themes.system) {
                    this.applySystemTheme();
                } else {
                    this.applyThemeToDOM(theme);
                }

                this.updateButtonStates();
            }

            setupEventListeners() {
                const systemBtn = document.getElementById('theme-system');
                const lightBtn = document.getElementById('theme-light');
                const darkBtn = document.getElementById('theme-dark');

                if (systemBtn) {
                    systemBtn.addEventListener('click', () => this.applyTheme(this.themes.system));
                }
                if (lightBtn) {
                    lightBtn.addEventListener('click', () => this.applyTheme(this.themes.light));
                }
                if (darkBtn) {
                    darkBtn.addEventListener('click', () => this.applyTheme(this.themes.dark));
                }
            }

            updateButtonStates() {
                const buttons = {
                    'theme-system': this.themes.system,
                    'theme-light': this.themes.light,
                    'theme-dark': this.themes.dark
                };

                Object.entries(buttons).forEach(([id, theme]) => {
                    const button = document.getElementById(id);
                    if (button) {
                        if (this.currentTheme === theme) {
                            button.classList.add('active');
                        } else {
                            button.classList.remove('active');
                        }
                    }
                });
            }

            getCurrentTheme() {
                return this.currentTheme;
            }

            isDarkMode() {
                if (this.currentTheme === this.themes.system) {
                    return this.getSystemTheme() === 'dark';
                }
                return this.currentTheme === this.themes.dark;
            }
        }

        // 初始化主题管理器
        const themeManager = new ThemeManager();

        // 站点数据 - 所有站点信息都集中在此数组中管理
        const sites = [
            {
                name: "LibreTV",
                icon: "fa-television",
                links: [
                    { url: "https://libretv-jsh40svv.edgeone.app/", platform: "EdgeOne", responseTime: null, status: 'unknown' },
                    { url: "https://libretv-3fh.pages.dev/", platform: "Cloudflare", responseTime: null, status: 'unknown' },
                    { url: "https://libretv.magiccode.dpdns.org/", platform: "MagicCode", responseTime: null, status: 'unknown' }
                ]
            },
            {
                name: "MoonTV",
                icon: "fa-moon-o",
                links: [
                    { url: "https://moontv-bnx.pages.dev/", platform: "Cloudflare", responseTime: null, status: 'unknown' },
                    { url: "https://moontv.magiccode.dpdns.org/", platform: "MagicCode", responseTime: null, status: 'unknown' }
                ]
            }
        ];

        // DOM元素
        const refreshBtn = document.getElementById('refresh-btn');
        const lastCheckedEl = document.querySelector('#last-checked span');
        const notification = document.getElementById('notification');
        const notificationText = document.getElementById('notification-text');
        const sitesContainer = document.getElementById('sites-container');

        // 统计面板元素
        const totalLinksEl = document.getElementById('total-links');
        const onlineLinksEl = document.getElementById('online-links');
        const offlineLinksEl = document.getElementById('offline-links');
        const successRateEl = document.getElementById('success-rate');

        // 显示通知
        function showNotification(message) {
            notificationText.textContent = message;
            notification.classList.remove('translate-y-20', 'opacity-0');
            notification.classList.add('translate-y-0', 'opacity-100');

            setTimeout(() => {
                notification.classList.remove('translate-y-0', 'opacity-100');
                notification.classList.add('translate-y-20', 'opacity-0');
            }, 3000);
        }

        // 排序站点链接 - 按响应时间和状态排序
        function sortSiteLinks(site) {
            site.links.sort((a, b) => {
                // 首先按状态排序：在线 > 离线/超时 > 未知
                const statusPriority = {
                    'online': 1,
                    'offline': 2,
                    'timeout': 2,
                    'unknown': 3
                };

                const statusDiff = statusPriority[a.status] - statusPriority[b.status];
                if (statusDiff !== 0) {
                    return statusDiff;
                }

                // 如果状态相同，按响应时间排序（在线状态下）
                if (a.status === 'online' && b.status === 'online') {
                    if (a.responseTime !== null && b.responseTime !== null) {
                        return a.responseTime - b.responseTime;
                    }
                    if (a.responseTime !== null) return -1;
                    if (b.responseTime !== null) return 1;
                }

                // 其他情况保持原顺序
                return 0;
            });
        }

        // 更新统计信息
        function updateStatistics() {
            let totalLinks = 0;
            let onlineLinks = 0;
            let offlineLinks = 0;

            // 计算总链接数
            sites.forEach(site => {
                totalLinks += site.links.length;
            });

            // 统计在线和离线链接数
            sites.forEach(site => {
                const linksContainer = document.getElementById(`${site.name.toLowerCase()}-links`);
                if (linksContainer) {
                    const linkItems = linksContainer.querySelectorAll('.link-item');
                    linkItems.forEach(linkItem => {
                        const statusText = linkItem.querySelector('.status-indicator span:last-child');
                        if (statusText) {
                            const status = statusText.textContent.toLowerCase();
                            if (status.includes('在线')) {
                                onlineLinks++;
                            } else if (status.includes('离线') || status.includes('超时')) {
                                offlineLinks++;
                            }
                        }
                    });
                }
            });

            // 计算成功率
            const checkedLinks = onlineLinks + offlineLinks;
            const successRate = checkedLinks > 0 ? Math.round((onlineLinks / checkedLinks) * 100) : 0;

            // 更新UI
            totalLinksEl.textContent = totalLinks;
            onlineLinksEl.textContent = onlineLinks;
            offlineLinksEl.textContent = offlineLinks;
            successRateEl.textContent = `${successRate}%`;
        }

        // 生成单个站点的链接HTML
        function generateSiteLinksHTML(site) {
            // 找到最快的在线链接
            const onlineLinks = site.links.filter(link => link.status === 'online' && link.responseTime !== null);
            const fastestLink = onlineLinks.length > 0 ?
                onlineLinks.reduce((fastest, current) =>
                    current.responseTime < fastest.responseTime ? current : fastest
                ) : null;

            return site.links.map((link, index) => {
                const isFastest = fastestLink && link.url === fastestLink.url;
                const isOnline = link.status === 'online';

                // 根据状态生成状态显示内容
                let statusContent = '';
                let statusDotClass = 'w-3 h-3 rounded-full bg-gray-400 mr-2 status-pulse';
                let statusText = '检查中...';
                let statusTextClass = 'text-sm text-gray-500';

                if (link.status === 'online') {
                    statusDotClass = 'w-3 h-3 rounded-full bg-success mr-2';
                    statusText = link.responseTime ? `在线 (${link.responseTime}ms)` : '在线';
                    statusTextClass = 'text-sm text-success font-medium';
                } else if (link.status === 'offline') {
                    statusDotClass = 'w-3 h-3 rounded-full bg-danger mr-2';
                    statusText = '离线';
                    statusTextClass = 'text-sm text-danger font-medium';
                } else if (link.status === 'timeout') {
                    statusDotClass = 'w-3 h-3 rounded-full bg-warning mr-2';
                    statusText = '超时';
                    statusTextClass = 'text-sm text-warning font-medium';
                }

                return `
                <li class="link-item cursor-pointer" data-url="${link.url}" title="点击访问 ${link.platform} 线路">
                    <div class="flex flex-col sm:flex-row sm:items-center justify-between bg-gray-50 dark:bg-gray-700 p-4 rounded-lg border border-gray-200 dark:border-gray-600 hover:border-primary/30 hover:bg-primary/5 dark:hover:bg-primary/10 transition-all ${isFastest ? 'ring-2 ring-success/20 bg-success/5 dark:bg-success/10' : ''}">
                        <div class="mb-2 sm:mb-0 flex-1">
                            <div class="font-medium mb-1 text-gray-800 dark:text-gray-200 flex items-center">
                                线路${index + 1} (${link.platform})
                                ${isFastest ?
                                    `<span class="ml-2 text-xs bg-success text-white px-2 py-1 rounded-full flex items-center">
                                        <i class="fa fa-bolt mr-1"></i>最快
                                    </span>` :
                                    isOnline ?
                                    `<span class="ml-2 text-xs bg-success/10 dark:bg-success/20 text-success px-2 py-1 rounded-full">可用</span>` : ''}
                            </div>
                        </div>
                        <div class="status-indicator flex items-center justify-between">
                            <div class="flex items-center mr-3">
                                <span class="${statusDotClass}"></span>
                                <span class="${statusTextClass}">${statusText}</span>
                            </div>
                            <button class="check-single-btn text-xs text-primary hover:text-primary/80 px-2 py-1 rounded border border-primary/20 hover:border-primary/40 transition-all"
                                    data-site="${site.name.toLowerCase()}" data-index="${index}" title="单独检查此线路">
                                <i class="fa fa-refresh"></i>
                            </button>
                        </div>
                    </div>
                </li>
                `;
            }).join('');
        }

        // 重新生成单个站点的链接列表
        function regenerateSiteLinks(site) {
            const linksContainer = document.getElementById(`${site.name.toLowerCase()}-links`);
            if (linksContainer) {
                linksContainer.innerHTML = generateSiteLinksHTML(site);
            }
        }

        // 生成站点卡片HTML
        function generateSiteCards() {
            sitesContainer.innerHTML = '';

            sites.forEach(site => {
                const siteCard = document.createElement('div');
                siteCard.className = 'bg-white dark:bg-gray-800 rounded-xl shadow-lg overflow-hidden card-hover transition-colors duration-300';

                // 站点头部
                siteCard.innerHTML = `
          <div class="bg-primary/10 dark:bg-primary/20 px-6 py-4 border-b border-primary/20 dark:border-primary/30">
            <div class="flex justify-between items-center">
              <h3 class="text-xl font-bold text-primary flex items-center">
                <i class="fa ${site.icon} mr-3 text-2xl"></i>
                ${site.name}
              </h3>
              <span class="bg-primary/20 dark:bg-primary/30 text-primary px-3 py-1 rounded-full text-sm font-medium">
                ${site.links.length}条线路
              </span>
            </div>
          </div>

          <div class="p-6">
            <!-- 线路列表 -->
            <div>
              <h4 class="font-semibold text-gray-700 dark:text-gray-300 mb-4">可用线路 <span class="text-xs text-gray-500 dark:text-gray-400">(按速度排序)</span></h4>
              <ul class="space-y-4" id="${site.name.toLowerCase()}-links">
                ${generateSiteLinksHTML(site)}
              </ul>
            </div>
          </div>
        `;

                sitesContainer.appendChild(siteCard);
            });
        }

        // 检测链接状态 - 改进版本
        async function checkLinkStatus(url, timeout = 10000) {
            const controller = new AbortController();
            const timeoutId = setTimeout(() => controller.abort(), timeout);

            try {
                // 尝试多种检测方法
                const methods = [
                    // 方法1: 使用GET请求检查（最可靠）
                    async () => {
                        const response = await fetch(url, {
                            method: 'GET',
                            signal: controller.signal,
                            cache: 'no-store',
                            redirect: 'follow'
                        });
                        return { success: response.ok, status: response.status, method: 'GET' };
                    },

                    // 方法2: 使用HEAD请求检查
                    async () => {
                        const response = await fetch(url, {
                            method: 'HEAD',
                            signal: controller.signal,
                            cache: 'no-store',
                            redirect: 'follow'
                        });
                        return { success: response.ok, status: response.status, method: 'HEAD' };
                    },

                    // 方法3: 使用no-cors模式（作为备选）
                    async () => {
                        const response = await fetch(url, {
                            method: 'GET',
                            mode: 'no-cors',
                            signal: controller.signal,
                            cache: 'no-store'
                        });
                        // no-cors模式下，只要没有抛出异常就认为可访问
                        return { success: true, status: 'opaque', method: 'no-cors' };
                    }
                ];

                // 依次尝试各种方法
                for (const method of methods) {
                    try {
                        const result = await method();
                        clearTimeout(timeoutId);
                        console.log(`URL: ${url}, Method: ${result.method}, Status: ${result.status}, Success: ${result.success}`);
                        return result.success;
                    } catch (error) {
                        // 如果当前方法失败，继续尝试下一个方法
                        console.log(`Method failed for ${url}:`, error.message);
                        continue;
                    }
                }

                // 所有方法都失败
                clearTimeout(timeoutId);
                return false;

            } catch (error) {
                clearTimeout(timeoutId);
                console.error(`All methods failed for ${url}:`, error);
                return false;
            }
        }

        // 带重试的链接状态检测
        async function checkLinkStatusWithRetry(url, maxRetries = 2) {
            for (let attempt = 1; attempt <= maxRetries; attempt++) {
                console.log(`Checking ${url} - Attempt ${attempt}/${maxRetries}`);

                const isOnline = await checkLinkStatus(url);

                if (isOnline) {
                    return true;
                }

                // 如果不是最后一次尝试，等待一段时间后重试
                if (attempt < maxRetries) {
                    await new Promise(resolve => setTimeout(resolve, 1000 * attempt));
                }
            }

            return false;
        }

        // 更新单个链接状态的UI
        function updateLinkStatusUI(linkElement, status, responseTime = null) {
            const statusIndicator = linkElement.querySelector('.status-indicator');
            const statusDot = statusIndicator.querySelector('span:first-child');
            const statusText = statusIndicator.querySelector('span:last-child');

            // 移除脉冲动画
            statusDot.classList.remove('status-pulse');

            if (status === 'checking') {
                statusDot.className = 'w-3 h-3 rounded-full bg-gray-400 mr-2 status-pulse';
                statusText.textContent = '检查中...';
                statusText.className = 'text-sm text-gray-500';
            } else if (status === 'online') {
                statusDot.className = 'w-3 h-3 rounded-full bg-success mr-2';
                const timeText = responseTime ? ` (${responseTime}ms)` : '';
                statusText.textContent = `在线${timeText}`;
                statusText.className = 'text-sm text-success font-medium';
            } else if (status === 'offline') {
                statusDot.className = 'w-3 h-3 rounded-full bg-danger mr-2';
                statusText.textContent = '离线';
                statusText.className = 'text-sm text-danger font-medium';
            } else if (status === 'timeout') {
                statusDot.className = 'w-3 h-3 rounded-full bg-warning mr-2';
                statusText.textContent = '超时';
                statusText.className = 'text-sm text-warning font-medium';
            }
        }

        // 检测单个链接并更新UI
        async function checkAndUpdateSingleLink(site, linkIndex) {
            const linksContainer = document.getElementById(`${site.name.toLowerCase()}-links`);
            const linkItem = linksContainer.querySelector(`.link-item:nth-child(${linkIndex + 1})`);
            const url = site.links[linkIndex].url;

            // 设置检查中状态
            updateLinkStatusUI(linkItem, 'checking');

            try {
                const startTime = Date.now();
                const isOnline = await checkLinkStatusWithRetry(url);
                const responseTime = Date.now() - startTime;

                // 更新链接数据
                if (isOnline) {
                    site.links[linkIndex].status = 'online';
                    site.links[linkIndex].responseTime = responseTime;
                    updateLinkStatusUI(linkItem, 'online', responseTime);
                } else {
                    site.links[linkIndex].status = 'offline';
                    site.links[linkIndex].responseTime = null;
                    updateLinkStatusUI(linkItem, 'offline');
                }

                // 排序并重新生成该站点的链接列表
                sortSiteLinks(site);
                regenerateSiteLinks(site);

                // 更新统计信息
                updateStatistics();

                return isOnline;
            } catch (error) {
                console.error(`Error checking ${url}:`, error);
                site.links[linkIndex].status = 'timeout';
                site.links[linkIndex].responseTime = null;
                updateLinkStatusUI(linkItem, 'timeout');

                // 排序并重新生成该站点的链接列表
                sortSiteLinks(site);
                regenerateSiteLinks(site);

                return false;
            }
        }

        // 更新所有链接状态 - 改进版本
        async function updateLinkStatuses() {
            // 显示刷新中状态
            refreshBtn.disabled = true;
            refreshBtn.innerHTML = '<i class="fa fa-spinner fa-spin mr-2"></i><span>检查中...</span>';

            // 更新最后检查时间
            const now = new Date();
            const formattedTime = now.toLocaleString('zh-CN', {
                year: 'numeric',
                month: '2-digit',
                day: '2-digit',
                hour: '2-digit',
                minute: '2-digit',
                second: '2-digit'
            });
            lastCheckedEl.textContent = `最后检查: ${formattedTime}`;

            // 统计信息
            let totalLinks = 0;
            let onlineLinks = 0;
            let checkedLinks = 0;

            // 计算总链接数
            sites.forEach(site => {
                totalLinks += site.links.length;
            });

            // 更新进度显示
            function updateProgress() {
                const progress = Math.round((checkedLinks / totalLinks) * 100);
                refreshBtn.innerHTML = `<i class="fa fa-spinner fa-spin mr-2"></i><span>检查中... ${progress}%</span>`;
            }

            try {
                // 并发检查所有链接（限制并发数量以避免过载）
                const concurrencyLimit = 3;
                const checkPromises = [];

                for (const site of sites) {
                    for (let i = 0; i < site.links.length; i++) {
                        const checkPromise = (async () => {
                            const url = site.links[i].url;

                            const startTime = Date.now();
                            const isOnline = await checkLinkStatusWithRetry(url);
                            const responseTime = Date.now() - startTime;

                            // 更新链接数据
                            if (isOnline) {
                                site.links[i].status = 'online';
                                site.links[i].responseTime = responseTime;
                            } else {
                                site.links[i].status = 'offline';
                                site.links[i].responseTime = null;
                            }

                            checkedLinks++;
                            if (isOnline) onlineLinks++;
                            updateProgress();
                            return isOnline;
                        })();

                        checkPromises.push(checkPromise);

                        // 控制并发数量
                        if (checkPromises.length >= concurrencyLimit) {
                            await Promise.all(checkPromises.splice(0, concurrencyLimit));
                        }
                    }
                }

                // 等待剩余的检查完成
                if (checkPromises.length > 0) {
                    await Promise.all(checkPromises);
                }

                // 所有检查完成后，对所有站点进行排序并重新生成
                sites.forEach(site => {
                    sortSiteLinks(site);
                    regenerateSiteLinks(site);
                });

                // 显示完成状态
                const successRate = Math.round((onlineLinks / totalLinks) * 100);
                let statusMessage = `状态检查完成！${onlineLinks}/${totalLinks} 条线路在线 (${successRate}%)`;

                // 根据成功率显示不同的状态信息
                if (successRate >= 80) {
                    statusMessage += ' 🟢';
                } else if (successRate >= 50) {
                    statusMessage += ' 🟡';
                } else {
                    statusMessage += ' 🔴';
                }

                showNotification(statusMessage);

                // 如果成功率较低，建议用户稍后重试
                if (successRate < 50 && onlineLinks > 0) {
                    setTimeout(() => {
                        showNotification('检测到部分线路可能暂时不可用，建议5分钟后重新检查');
                    }, 4000);
                }

            } catch (error) {
                console.error('Error during status update:', error);
                showNotification('状态检查过程中出现错误，请稍后重试');
            } finally {
                // 恢复刷新按钮
                refreshBtn.disabled = false;
                refreshBtn.innerHTML = '<i class="fa fa-refresh mr-2"></i><span>刷新状态</span>';

                // 最终更新统计信息
                updateStatistics();
            }
        }

        // 单独检查某个链接
        async function checkSingleLink(siteName, originalIndex) {
            const site = sites.find(s => s.name.toLowerCase() === siteName);
            if (!site) {
                console.error('Invalid site');
                return;
            }

            // 找到当前显示顺序中对应的链接
            const linksContainer = document.getElementById(`${siteName}-links`);
            const linkItems = linksContainer.querySelectorAll('.link-item');

            if (!linkItems[originalIndex]) {
                console.error('Invalid link index');
                return;
            }

            const linkItem = linkItems[originalIndex];
            const url = linkItem.dataset.url;
            const linkData = site.links.find(link => link.url === url);

            if (!linkData) {
                console.error('Link data not found');
                return;
            }

            // 设置检查中状态
            updateLinkStatusUI(linkItem, 'checking');

            try {
                const startTime = Date.now();
                const isOnline = await checkLinkStatusWithRetry(url);
                const responseTime = Date.now() - startTime;

                // 更新链接数据
                if (isOnline) {
                    linkData.status = 'online';
                    linkData.responseTime = responseTime;
                } else {
                    linkData.status = 'offline';
                    linkData.responseTime = null;
                }

                // 排序并重新生成该站点的链接列表
                sortSiteLinks(site);
                regenerateSiteLinks(site);

                // 更新统计信息
                updateStatistics();

                showNotification(`${site.name} 线路状态已更新`);
            } catch (error) {
                console.error(`Error checking ${url}:`, error);
                linkData.status = 'timeout';
                linkData.responseTime = null;

                // 排序并重新生成该站点的链接列表
                sortSiteLinks(site);
                regenerateSiteLinks(site);

                showNotification(`${site.name} 线路检查失败`);
            }
        }

        // 自动定时检查功能
        let autoCheckInterval = null;

        function startAutoCheck(intervalMinutes = 30) {
            if (autoCheckInterval) {
                clearInterval(autoCheckInterval);
            }

            autoCheckInterval = setInterval(() => {
                console.log('Auto checking link statuses...');
                updateLinkStatuses();
            }, intervalMinutes * 60 * 1000);

            console.log(`Auto check started, interval: ${intervalMinutes} minutes`);
        }

        function stopAutoCheck() {
            if (autoCheckInterval) {
                clearInterval(autoCheckInterval);
                autoCheckInterval = null;
                console.log('Auto check stopped');
            }
        }

        // 添加键盘快捷键支持
        function setupKeyboardShortcuts() {
            document.addEventListener('keydown', (e) => {
                // Ctrl+R 或 F5: 刷新状态
                if ((e.ctrlKey && e.key === 'r') || e.key === 'F5') {
                    e.preventDefault();
                    if (!refreshBtn.disabled) {
                        updateLinkStatuses();
                    }
                }

                // Ctrl+Shift+R: 强制刷新所有状态
                if (e.ctrlKey && e.shiftKey && e.key === 'R') {
                    e.preventDefault();
                    if (!refreshBtn.disabled) {
                        // 清除所有缓存并重新检查
                        location.reload();
                    }
                }
            });
        }

        // 初始化页面
        function init() {
            // 生成站点卡片
            generateSiteCards();

            // 初始化统计信息
            updateStatistics();

            // 添加刷新按钮事件监听
            refreshBtn.addEventListener('click', updateLinkStatuses);

            // 添加点击事件监听
            document.addEventListener('click', (e) => {
                // 处理单独检查按钮点击
                if (e.target.closest('.check-single-btn')) {
                    e.stopPropagation(); // 阻止事件冒泡，避免触发li的点击事件
                    const btn = e.target.closest('.check-single-btn');
                    const siteName = btn.dataset.site;
                    const linkIndex = parseInt(btn.dataset.index);
                    checkSingleLink(siteName, linkIndex);
                    return;
                }

                // 处理线路项点击（打开新标签）
                if (e.target.closest('.link-item')) {
                    const linkItem = e.target.closest('.link-item');
                    const url = linkItem.dataset.url;

                    if (url) {
                        // 在新标签中打开链接
                        window.open(url, '_blank', 'noopener,noreferrer');

                        // 显示通知
                        const platform = linkItem.querySelector('.font-medium').textContent;
                        showNotification(`正在打开 ${platform}...`);
                    }
                }
            });

            // 设置键盘快捷键
            setupKeyboardShortcuts();

            // 启动自动检查（30分钟间隔）
            startAutoCheck(30);

            // 页面加载完成后自动检查一次状态
            setTimeout(updateLinkStatuses, 1000);

            // 页面可见性变化时的处理
            document.addEventListener('visibilitychange', () => {
                if (document.visibilityState === 'visible') {
                    // 页面重新可见时，检查是否需要更新状态
                    const lastCheck = lastCheckedEl.textContent;
                    if (lastCheck.includes('从未')) {
                        updateLinkStatuses();
                    }
                }
            });
        }

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', init);
    </script>
</body>

</html>