<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>免费影视导航</title>
    <meta name="description" content="免费影视导航 - 提供多个免费影视站点的访问链接，实时检测线路状态">
    <meta name="keywords" content="免费影视,在线观看,影视导航,电影,电视剧">

    <!-- 外部资源 -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/4.7.0/css/font-awesome.min.css">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">

    <!-- 本地样式文件 -->
    <link rel="stylesheet" href="styles.css">

    <!-- Tailwind CSS 配置 -->
    <script>
        tailwind.config = {
            darkMode: 'class',
            theme: {
                extend: {
                    colors: {
                        primary: '#165DFF',
                        success: '#10B981',
                        warning: '#F59E0B',
                        danger: '#EF4444'
                    },
                    fontFamily: {
                        'inter': ['Inter', 'sans-serif']
                    }
                }
            }
        }
    </script>
    <script src="https://cdn.tailwindcss.com"></script>
</head>

<body
    class="bg-gradient-to-br from-gray-50 to-gray-100 dark:from-gray-900 dark:to-gray-800 min-h-screen font-inter text-dark dark:text-gray-100 transition-colors duration-300">
    <!-- 可爱风格加载动画 -->
    <div id="loading-overlay" class="loading-overlay">
        <div class="loading-container">
            <!-- 旋转圆圈动画 -->
            <div class="loading-spinner">
                <div class="loading-circle"></div>
                <div class="loading-circle"></div>
                <div class="loading-circle"></div>
            </div>

            <!-- 弹跳小点动画 -->
            <div class="loading-dots">
                <div class="loading-dot"></div>
                <div class="loading-dot"></div>
                <div class="loading-dot"></div>
            </div>

            <!-- 加载文字 -->
            <div class="loading-text">正在加载影视导航</div>
            <div class="loading-subtitle">
                请稍候，正在为您准备精彩内容
                <span class="loading-heart">♥</span>
            </div>
        </div>
    </div>

    <!-- 顶部导航 -->
    <header class="sticky top-0 z-50 transition-all duration-300">
        <div class="container mx-auto px-3 sm:px-4 py-3 sm:py-4 flex justify-between items-center">
            <div class="flex items-center">
                <i class="fa fa-play-circle text-primary text-xl sm:text-2xl"></i>
            </div>
            <div class="flex items-center space-x-2 sm:space-x-4">
                <!-- 主题切换下拉菜单 -->
                <div class="relative">
                    <button id="theme-toggle"
                        class="bg-gray-100 dark:bg-gray-700 hover:bg-gray-200 dark:hover:bg-gray-600 p-2 sm:px-3 sm:py-2 rounded-lg flex items-center space-x-1 transition-all duration-200"
                        title="切换主题">
                        <i id="theme-icon" class="fa fa-desktop text-sm"></i>
                        <span id="theme-text" class="hidden lg:inline text-xs font-medium">系统</span>
                        <i class="fa fa-chevron-down text-xs hidden sm:inline ml-1"></i>
                    </button>
                    <!-- 下拉菜单 -->
                    <div id="theme-dropdown"
                        class="absolute right-0 mt-2 w-32 bg-white dark:bg-gray-800 rounded-lg shadow-lg border border-gray-200 dark:border-gray-600 opacity-0 invisible transform scale-95 transition-all duration-200 z-50">
                        <div class="py-1">
                            <button
                                class="theme-option w-full text-left px-3 py-2 text-sm hover:bg-gray-100 dark:hover:bg-gray-700 flex items-center space-x-2"
                                data-theme="system">
                                <i class="fa fa-desktop w-4"></i>
                                <span>跟随系统</span>
                            </button>
                            <button
                                class="theme-option w-full text-left px-3 py-2 text-sm hover:bg-gray-100 dark:hover:bg-gray-700 flex items-center space-x-2"
                                data-theme="light">
                                <i class="fa fa-sun-o w-4"></i>
                                <span>白天模式</span>
                            </button>
                            <button
                                class="theme-option w-full text-left px-3 py-2 text-sm hover:bg-gray-100 dark:hover:bg-gray-700 flex items-center space-x-2"
                                data-theme="dark">
                                <i class="fa fa-moon-o w-4"></i>
                                <span>黑夜模式</span>
                            </button>
                        </div>
                    </div>
                </div>

                <span id="last-checked" class="text-xs text-gray-500 dark:text-gray-400 hidden lg:inline-block">
                    <i class="fa fa-clock-o mr-1"></i>
                    <span>最后检查: 从未</span>
                </span>
                <button id="refresh-btn"
                    class="bg-primary hover:bg-primary/90 text-white p-2 sm:px-3 sm:py-2 rounded-lg flex items-center transition-all relative"
                    title="刷新状态">
                    <i id="refresh-icon" class="fa fa-refresh text-sm transition-transform duration-300"></i>
                </button>
            </div>
        </div>
    </header>

    <!-- 主内容区 -->
    <main class="container mx-auto px-4 py-8">
        <!-- 介绍部分 -->
        <div class="text-center mb-10 max-w-3xl mx-auto">
            <h1 class="text-2xl sm:text-3xl md:text-4xl font-bold text-primary mb-6 flex items-center justify-center">
                <!-- <i class="fa fa-play-circle mr-3"></i> -->
                免费影视导航
            </h1>
            <p class="text-gray-600 dark:text-gray-300 text-lg">以下是可用的免费影视站点，每个站点提供多条访问线路。绿色标识表示线路可正常访问，红色表示线路暂时不可用。</p>
        </div>

        <!-- 状态统计面板 -->
        <div id="status-panel"
            class="bg-white dark:bg-gray-800 rounded-xl shadow-lg p-6 mb-8 max-w-4xl mx-auto transition-colors duration-300">
            <h3 class="text-lg font-bold mb-4 flex items-center text-gray-900 dark:text-gray-100">
                <i class="fa fa-bar-chart text-primary mr-2"></i>线路状态统计
            </h3>
            <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
                <div class="text-center p-4 bg-gray-50 dark:bg-gray-700 rounded-lg transition-colors duration-300">
                    <div class="text-2xl font-bold text-gray-700 dark:text-gray-200" id="total-links">-</div>
                    <div class="text-sm text-gray-500 dark:text-gray-400">总线路数</div>
                </div>
                <div class="text-center p-4 bg-success/10 dark:bg-success/20 rounded-lg transition-colors duration-300">
                    <div class="text-2xl font-bold text-success" id="online-links">-</div>
                    <div class="text-sm text-success">在线线路</div>
                </div>
                <div class="text-center p-4 bg-danger/10 dark:bg-danger/20 rounded-lg transition-colors duration-300">
                    <div class="text-2xl font-bold text-danger" id="offline-links">-</div>
                    <div class="text-sm text-danger">离线线路</div>
                </div>
                <div class="text-center p-4 bg-primary/10 dark:bg-primary/20 rounded-lg transition-colors duration-300">
                    <div class="text-2xl font-bold text-primary" id="success-rate">-</div>
                    <div class="text-sm text-primary">成功率</div>
                </div>
            </div>
        </div>

        <!-- 站点卡片容器 -->
        <div id="sites-container" class="grid grid-cols-1 md:grid-cols-2 gap-8 mb-12">
            <!-- 站点将通过JavaScript动态生成 -->
        </div>

        <!-- 使用说明 -->
        <div
            class="bg-white dark:bg-gray-800 rounded-xl shadow-md p-6 max-w-3xl mx-auto transition-colors duration-300">
            <h3 class="text-xl font-bold mb-4 flex items-center text-gray-900 dark:text-gray-100">
                <i class="fa fa-info-circle text-primary mr-2"></i>使用说明
            </h3>
            <ul class="space-y-3 text-gray-600 dark:text-gray-300">
                <li class="flex items-start">
                    <i class="fa fa-check-circle text-success mt-1 mr-3"></i>
                    <span>点击线路链接将在新窗口打开对应网站</span>
                </li>
                <li class="flex items-start">
                    <i class="fa fa-check-circle text-success mt-1 mr-3"></i>
                    <span>如果某条线路无法访问，请尝试其他线路</span>
                </li>
                <li class="flex items-start">
                    <i class="fa fa-check-circle text-success mt-1 mr-3"></i>
                    <span>点击"刷新状态"按钮可以重新检查所有线路的可用性</span>
                </li>
            </ul>
        </div>
    </main>

    <!-- 页脚 -->
    <footer class="mt-16 py-8">
        <div class="container mx-auto px-4 text-center">
            <p class="mb-4 text-gray-600 dark:text-gray-300">免费影视站点导航</p>
            <p class="text-gray-500 dark:text-gray-400 text-sm">© 2025 免费影视站点导航页面 | 所有链接均搜集自互联网</p>
        </div>
    </footer>

    <!-- 通知提示 -->
    <div id="notification"
        class="fixed bottom-6 right-6 bg-dark dark:bg-gray-700 text-white px-6 py-3 rounded-lg shadow-lg transform translate-y-20 opacity-0 transition-all duration-300 flex items-center">
        <i class="fa fa-check-circle mr-2"></i>
        <span id="notification-text">操作成功</span>
    </div>

    <!-- 引入外部JavaScript文件 -->
    <script src="script.js"></script>
</body>

</html>