// 免费影视导航 - 主脚本文件

// 控制台样式工具
const ConsoleStyles = {
    // 颜色样式
    colors: {
        primary: 'color: #165DFF; font-weight: bold;',
        success: 'color: #10B981; font-weight: bold;',
        warning: 'color: #F59E0B; font-weight: bold;',
        danger: 'color: #EF4444; font-weight: bold;',
        info: 'color: #3B82F6; font-weight: bold;',
        muted: 'color: #6B7280;'
    },

    // 背景样式
    backgrounds: {
        primary: 'background: #165DFF; color: white; padding: 2px 6px; border-radius: 3px;',
        success: 'background: #10B981; color: white; padding: 2px 6px; border-radius: 3px;',
        warning: 'background: #F59E0B; color: white; padding: 2px 6px; border-radius: 3px;',
        danger: 'background: #EF4444; color: white; padding: 2px 6px; border-radius: 3px;',
        info: 'background: #3B82F6; color: white; padding: 2px 6px; border-radius: 3px;'
    },

    // 图标
    icons: {
        init: '🚀',
        loading: '⏳',
        success: '✅',
        error: '❌',
        warning: '⚠️',
        info: 'ℹ️',
        theme: '🎨',
        network: '🌐',
        config: '⚙️',
        stats: '📊'
    },

    // 日志方法
    log: {
        init: (message, ...args) => console.log(`%c${ConsoleStyles.icons.init} 初始化`, ConsoleStyles.backgrounds.primary, message, ...args),
        loading: (message, ...args) => console.log(`%c${ConsoleStyles.icons.loading} 加载中`, ConsoleStyles.backgrounds.info, message, ...args),
        success: (message, ...args) => console.log(`%c${ConsoleStyles.icons.success} 成功`, ConsoleStyles.backgrounds.success, message, ...args),
        error: (message, ...args) => console.error(`%c${ConsoleStyles.icons.error} 错误`, ConsoleStyles.backgrounds.danger, message, ...args),
        warning: (message, ...args) => console.warn(`%c${ConsoleStyles.icons.warning} 警告`, ConsoleStyles.backgrounds.warning, message, ...args),
        info: (message, ...args) => console.info(`%c${ConsoleStyles.icons.info} 信息`, ConsoleStyles.backgrounds.info, message, ...args),
        theme: (message, ...args) => console.log(`%c${ConsoleStyles.icons.theme} 主题`, ConsoleStyles.backgrounds.primary, message, ...args),
        network: (message, ...args) => console.log(`%c${ConsoleStyles.icons.network} 网络`, ConsoleStyles.backgrounds.info, message, ...args),
        config: (message, ...args) => console.log(`%c${ConsoleStyles.icons.config} 配置`, ConsoleStyles.backgrounds.warning, message, ...args),
        stats: (message, ...args) => console.log(`%c${ConsoleStyles.icons.stats} 统计`, ConsoleStyles.backgrounds.success, message, ...args)
    },

    // 分组日志方法
    group: {
        start: (title, collapsed = false) => {
            if (collapsed) {
                console.groupCollapsed(`%c📁 ${title}`, 'color: #6B7280; font-weight: bold;');
            } else {
                console.group(`%c📂 ${title}`, 'color: #374151; font-weight: bold;');
            }
        },
        end: () => console.groupEnd()
    }
};

// 全局变量
let sites = [];
let themeManager, loadingManager;

// DOM元素
let refreshBtn, lastCheckedEl, notification, notificationText, sitesContainer;
let totalLinksEl, onlineLinksEl, offlineLinksEl, successRateEl;

// 主题管理器
class ThemeManager {
    constructor() {
        this.themes = {
            system: 'system',
            light: 'light',
            dark: 'dark'
        };
        this.currentTheme = this.getStoredTheme() || this.themes.system;
        ConsoleStyles.log.theme('ThemeManager 已初始化', { currentTheme: this.currentTheme });
        this.init();
    }

    init() {
        this.applyTheme(this.currentTheme);
        this.setupEventListeners();
        this.updateButtonStates();
    }

    getStoredTheme() {
        return localStorage.getItem('theme');
    }

    setStoredTheme(theme) {
        localStorage.setItem('theme', theme);
    }

    getSystemTheme() {
        return window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light';
    }

    applyTheme(theme) {
        const html = document.documentElement;
        
        // 移除所有主题类
        html.classList.remove('dark');
        
        let effectiveTheme = theme;
        if (theme === 'system') {
            effectiveTheme = this.getSystemTheme();
        }
        
        if (effectiveTheme === 'dark') {
            html.classList.add('dark');
        }

        this.currentTheme = theme;
        this.setStoredTheme(theme);

        ConsoleStyles.log.theme(`主题已切换: ${theme}`, {
            effectiveTheme,
            isDark: effectiveTheme === 'dark'
        });
    }

    setupEventListeners() {
        const themeToggle = document.getElementById('theme-toggle');
        const themeDropdown = document.getElementById('theme-dropdown');
        const themeOptions = document.querySelectorAll('.theme-option');

        ConsoleStyles.log.theme('设置主题事件监听器', {
            themeToggle: !!themeToggle,
            themeDropdown: !!themeDropdown,
            themeOptionsCount: themeOptions.length
        });

        // 主题切换按钮点击事件
        if (themeToggle && themeDropdown) {
            themeToggle.addEventListener('click', (e) => {
                e.stopPropagation();
                ConsoleStyles.log.theme('主题切换按钮被点击');

                // 切换下拉菜单显示状态
                const isVisible = themeDropdown.classList.contains('opacity-100');

                if (isVisible) {
                    // 隐藏下拉菜单
                    themeDropdown.classList.remove('opacity-100', 'visible', 'scale-100');
                    themeDropdown.classList.add('opacity-0', 'invisible', 'scale-95');
                } else {
                    // 显示下拉菜单
                    themeDropdown.classList.remove('opacity-0', 'invisible', 'scale-95');
                    themeDropdown.classList.add('opacity-100', 'visible', 'scale-100');
                }

                ConsoleStyles.log.theme('下拉菜单状态切换', {
                    isVisible: !isVisible,
                    classes: themeDropdown.className
                });
            });
        } else {
            ConsoleStyles.log.error('主题切换元素未找到', {
                themeToggle: !!themeToggle,
                themeDropdown: !!themeDropdown
            });
        }

        // 主题选项点击事件
        if (themeOptions.length > 0 && themeDropdown) {
            themeOptions.forEach(option => {
                option.addEventListener('click', (e) => {
                    e.stopPropagation();
                    const theme = option.dataset.theme;
                    ConsoleStyles.log.theme(`选择主题: ${theme}`);
                    this.applyTheme(theme);
                    this.updateButtonStates();

                    // 隐藏下拉菜单
                    themeDropdown.classList.remove('opacity-100', 'visible', 'scale-100');
                    themeDropdown.classList.add('opacity-0', 'invisible', 'scale-95');
                });
            });
        }

        // 点击其他地方关闭下拉菜单
        if (themeDropdown) {
            document.addEventListener('click', () => {
                // 隐藏下拉菜单
                themeDropdown.classList.remove('opacity-100', 'visible', 'scale-100');
                themeDropdown.classList.add('opacity-0', 'invisible', 'scale-95');
            });
        }

        // 监听系统主题变化
        window.matchMedia('(prefers-color-scheme: dark)').addEventListener('change', () => {
            if (this.currentTheme === 'system') {
                this.applyTheme('system');
            }
        });
    }

    updateButtonStates() {
        // 更新主题切换按钮的图标和文字
        const themeIcon = document.getElementById('theme-icon');
        const themeText = document.getElementById('theme-text');
        
        const themeConfig = {
            system: { icon: 'fa-desktop', text: '系统' },
            light: { icon: 'fa-sun-o', text: '白天' },
            dark: { icon: 'fa-moon-o', text: '黑夜' }
        };

        const config = themeConfig[this.currentTheme];
        if (themeIcon && config) {
            themeIcon.className = `fa ${config.icon} text-sm`;
        }
        if (themeText && config) {
            themeText.textContent = config.text;
        }

        // 更新下拉菜单中的选中状态
        const themeOptions = document.querySelectorAll('.theme-option');
        themeOptions.forEach(option => {
            const theme = option.dataset.theme;
            if (theme === this.currentTheme) {
                option.classList.add('active');
            } else {
                option.classList.remove('active');
            }
        });
    }
}

// 加载动画管理
class LoadingManager {
    constructor() {
        this.overlay = document.getElementById('loading-overlay');
        this.isVisible = true;
        ConsoleStyles.log.init('LoadingManager 已初始化', { overlay: this.overlay });
    }

    show() {
        if (this.overlay) {
            this.overlay.classList.remove('hidden');
            this.isVisible = true;
        }
    }

    hide() {
        ConsoleStyles.log.loading('隐藏加载动画');
        if (this.overlay) {
            ConsoleStyles.log.success('添加 hidden 类到加载覆盖层');
            this.overlay.classList.add('hidden');
            this.isVisible = false;

            // 延迟移除元素，等待动画完成
            setTimeout(() => {
                if (!this.isVisible && this.overlay) {
                    ConsoleStyles.log.success('设置加载覆盖层 display: none');
                    this.overlay.style.display = 'none';
                }
            }, 300);
        } else {
            ConsoleStyles.log.error('加载覆盖层元素未找到');
        }
    }

    updateText(text, subtitle = '') {
        const textEl = this.overlay?.querySelector('.loading-text');
        const subtitleEl = this.overlay?.querySelector('.loading-subtitle');
        
        if (textEl) textEl.textContent = text;
        if (subtitleEl && subtitle) {
            subtitleEl.innerHTML = subtitle + ' <span class="loading-heart">♥</span>';
        }
    }
}

// 加载站点配置
async function loadSitesConfig() {
    try {
        ConsoleStyles.log.config('正在加载 sites.json 配置文件...');
        const response = await fetch('sites.json');
        ConsoleStyles.log.network(`响应状态: ${response.status}`);

        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }

        const data = await response.json();
        sites = data.sites;
        ConsoleStyles.log.success(`站点配置加载成功: ${sites.length} 个站点`);
        return true;
    } catch (error) {
        ConsoleStyles.log.error('站点配置加载失败:', error.message);
        // 如果加载失败，使用默认配置
        ConsoleStyles.log.warning('使用备用配置');
        sites = [
            {
                name: "LibreTV",
                icon: "fa-television",
                links: [
                    { url: "https://libretv-jsh40svv.edgeone.app/", platform: "EdgeOne", responseTime: null, status: 'unknown' },
                    { url: "https://libretv-3fh.pages.dev/", platform: "Cloudflare", responseTime: null, status: 'unknown' },
                    { url: "https://libretv.magiccode.dpdns.org/", platform: "MagicCode", responseTime: null, status: 'unknown' }
                ]
            },
            {
                name: "MoonTV",
                icon: "fa-moon-o",
                links: [
                    { url: "https://moontv-bnx.pages.dev/", platform: "Cloudflare", responseTime: null, status: 'unknown' },
                    { url: "https://moontv.magiccode.dpdns.org/", platform: "MagicCode", responseTime: null, status: 'unknown' }
                ]
            },
            {
                name: "网盘资源",
                icon: "fa-cloud-download",
                links: [
                    { url: "https://www.aipan.me/", platform: "爱盼", responseTime: null, status: 'unknown' },
                    { url: "https://cloudsaver-test.160621.xyz/resource", platform: "云转存", responseTime: null, status: 'unknown' }
                ]
            }
        ];
        ConsoleStyles.log.success(`备用站点配置加载完成: ${sites.length} 个站点`);
        return false;
    }
}

// 显示通知
function showNotification(message) {
    if (notificationText && notification) {
        notificationText.textContent = message;
        notification.classList.remove('translate-y-20', 'opacity-0');
        notification.classList.add('translate-y-0', 'opacity-100');

        setTimeout(() => {
            notification.classList.remove('translate-y-0', 'opacity-100');
            notification.classList.add('translate-y-20', 'opacity-0');
        }, 3000);
    }
}

// 更新统计信息
function updateStatistics() {
    if (!totalLinksEl || !onlineLinksEl || !offlineLinksEl || !successRateEl) return;

    let totalLinks = 0;
    let onlineLinks = 0;
    let offlineLinks = 0;

    // 统计在线和离线链接数
    sites.forEach(site => {
        const linksContainer = document.getElementById(`${site.name.toLowerCase()}-links`);
        if (linksContainer) {
            const linkItems = linksContainer.querySelectorAll('.link-item');
            linkItems.forEach(linkItem => {
                totalLinks++;
                const statusText = linkItem.querySelector('.text-success, .text-red-500, .text-yellow-500');
                if (statusText) {
                    if (statusText.classList.contains('text-success')) {
                        onlineLinks++;
                    } else if (statusText.classList.contains('text-red-500')) {
                        offlineLinks++;
                    }
                }
            });
        }
    });

    const successRate = totalLinks > 0 ? Math.round((onlineLinks / totalLinks) * 100) : 0;

    totalLinksEl.textContent = totalLinks;
    onlineLinksEl.textContent = onlineLinks;
    offlineLinksEl.textContent = offlineLinks;
    successRateEl.textContent = `${successRate}%`;

    // 美化的统计信息输出
    ConsoleStyles.log.stats('链接状态统计更新', {
        总链接数: totalLinks,
        在线链接: onlineLinks,
        离线链接: offlineLinks,
        成功率: `${successRate}%`
    });
}

// 生成单个站点的链接HTML
function generateSiteLinksHTML(site) {
    return site.links.map((link, index) => {
        let statusDotClass = 'w-3 h-3 rounded-full bg-gray-400 mr-2';
        let statusText = '未检查';
        let statusTextClass = 'text-sm text-gray-500';
        let isFastest = false;

        if (link.status === 'online') {
            statusDotClass = 'w-3 h-3 rounded-full bg-success mr-2';
            statusText = link.responseTime ? `在线 (${link.responseTime}ms)` : '在线';
            statusTextClass = 'text-sm text-success font-medium';
        } else if (link.status === 'offline') {
            statusDotClass = 'w-3 h-3 rounded-full bg-red-500 mr-2';
            statusText = '离线';
            statusTextClass = 'text-sm text-red-500 font-medium';
        } else if (link.status === 'timeout') {
            statusDotClass = 'w-3 h-3 rounded-full bg-yellow-500 mr-2';
            statusText = '超时';
            statusTextClass = 'text-sm text-yellow-500 font-medium';
        }

        return `
        <li class="link-item cursor-pointer" data-url="${link.url}" title="点击访问 ${link.platform} 线路">
            <div class="flex flex-col sm:flex-row sm:items-center justify-between bg-gray-50 dark:bg-gray-700 p-4 rounded-lg border border-gray-200 dark:border-gray-600 hover:border-primary/30 hover:bg-primary/5 dark:hover:bg-primary/10 transition-all ${isFastest ? 'ring-2 ring-success/20 bg-success/5 dark:bg-success/10' : ''}">
                <div class="mb-2 sm:mb-0 flex-1">
                    <div class="flex items-center">
                        <span class="font-medium text-gray-900 dark:text-gray-100">${link.platform}</span>
                        ${isFastest ? '<span class="ml-2 px-2 py-1 text-xs bg-success text-white rounded-full">最快</span>' : ''}
                    </div>
                </div>
                <div class="flex items-center justify-between sm:justify-end space-x-3">
                    <div class="flex items-center">
                        <div class="${statusDotClass}"></div>
                        <span class="${statusTextClass}">${statusText}</span>
                    </div>
                    <button class="check-single-btn text-xs text-primary hover:text-primary/80 px-2 py-1 rounded border border-primary/20 hover:border-primary/40 transition-all"
                            data-site="${site.name.toLowerCase()}" data-index="${index}" title="单独检查此线路">
                        <i class="fa fa-refresh"></i>
                    </button>
                </div>
            </div>
        </li>
        `;
    }).join('');
}

// 重新生成单个站点的链接列表
function regenerateSiteLinks(site) {
    const linksContainer = document.getElementById(`${site.name.toLowerCase()}-links`);
    if (linksContainer) {
        linksContainer.innerHTML = generateSiteLinksHTML(site);
        addLinkEventListeners();
        addSingleCheckEventListeners();
    }
}

// 生成站点卡片
function generateSiteCards() {
    ConsoleStyles.log.init('生成站点卡片', { sitesContainer, sitesCount: sites.length });
    if (!sitesContainer) {
        ConsoleStyles.log.error('站点容器元素未找到');
        return;
    }

    sitesContainer.innerHTML = sites.map(site => `
        <div class="site-card bg-white dark:bg-gray-800 rounded-xl shadow-lg p-6 border border-gray-200 dark:border-gray-700 hover:shadow-xl transition-all duration-300">
            <div class="flex items-center mb-4">
                <i class="fa ${site.icon} text-2xl text-primary mr-3"></i>
                <h3 class="text-xl font-bold text-gray-900 dark:text-gray-100">${site.name}</h3>
            </div>
            <ul id="${site.name.toLowerCase()}-links" class="space-y-3">
                ${generateSiteLinksHTML(site)}
            </ul>
        </div>
    `).join('');

    addLinkEventListeners();
    addSingleCheckEventListeners();
}

// 添加链接点击事件监听器
function addLinkEventListeners() {
    document.querySelectorAll('.link-item').forEach(item => {
        item.addEventListener('click', function() {
            const url = this.dataset.url;
            if (url) {
                window.open(url, '_blank');
            }
        });
    });
}

// 添加单独检查按钮事件监听器
function addSingleCheckEventListeners() {
    document.querySelectorAll('.check-single-btn').forEach(btn => {
        btn.addEventListener('click', async function(e) {
            e.stopPropagation();
            const siteName = this.dataset.site;
            const linkIndex = parseInt(this.dataset.index);

            const site = sites.find(s => s.name.toLowerCase() === siteName);
            if (site) {
                this.innerHTML = '<i class="fa fa-spinner fa-spin"></i>';
                this.disabled = true;

                await checkAndUpdateSingleLink(site, linkIndex);

                this.innerHTML = '<i class="fa fa-refresh"></i>';
                this.disabled = false;

                updateStatistics();
            }
        });
    });
}

// 链接状态检查方法
async function checkLinkStatus(url) {
    const timeout = 10000; // 10秒超时

    const methods = [
        // 方法1: 使用 fetch 进行 HEAD 请求
        async () => {
            const controller = new AbortController();
            const timeoutId = setTimeout(() => controller.abort(), timeout);

            try {
                const response = await fetch(url, {
                    method: 'HEAD',
                    mode: 'no-cors',
                    signal: controller.signal
                });
                clearTimeout(timeoutId);
                return { success: true, method: 'HEAD', status: response.status };
            } catch (error) {
                clearTimeout(timeoutId);
                throw error;
            }
        },

        // 方法2: 使用 fetch 进行 GET 请求
        async () => {
            const controller = new AbortController();
            const timeoutId = setTimeout(() => controller.abort(), timeout);

            try {
                const response = await fetch(url, {
                    method: 'GET',
                    mode: 'no-cors',
                    signal: controller.signal
                });
                clearTimeout(timeoutId);
                return { success: true, method: 'GET', status: response.status };
            } catch (error) {
                clearTimeout(timeoutId);
                throw error;
            }
        },

        // 方法3: 使用图片加载测试
        async () => {
            return new Promise((resolve, reject) => {
                const img = new Image();
                const timeoutId = setTimeout(() => {
                    reject(new Error('Image load timeout'));
                }, timeout);

                img.onload = () => {
                    clearTimeout(timeoutId);
                    resolve({ success: true, method: 'Image', status: 200 });
                };

                img.onerror = () => {
                    clearTimeout(timeoutId);
                    resolve({ success: true, method: 'Image', status: 'accessible' });
                };

                img.src = url + '/favicon.ico?' + Date.now();
            });
        }
    ];

    // 尝试所有方法
    for (const method of methods) {
        try {
            const timeoutId = setTimeout(() => {
                throw new Error('Method timeout');
            }, timeout);

            try {
                const result = await method();
                clearTimeout(timeoutId);
                const statusIcon = result.success ? '✅' : '❌';
                ConsoleStyles.log.network(`${statusIcon} ${url}`, {
                    method: result.method,
                    status: result.status,
                    success: result.success
                });
                return result.success;
            } catch (error) {
                // 如果当前方法失败，继续尝试下一个方法
                clearTimeout(timeoutId);
                ConsoleStyles.log.warning(`方法失败 ${url}:`, error.message);
                continue;
            }
        } catch (error) {
            continue;
        }
    }

    ConsoleStyles.log.error(`所有检查方法都失败了: ${url}`);
    return false;
}

// 带重试的链接状态检查
async function checkLinkStatusWithRetry(url, maxRetries = 2) {
    for (let attempt = 1; attempt <= maxRetries; attempt++) {
        ConsoleStyles.log.network(`🔄 检查链接 (${attempt}/${maxRetries})`, { url });

        const isOnline = await checkLinkStatus(url);
        if (isOnline) {
            return true;
        }

        if (attempt < maxRetries) {
            await new Promise(resolve => setTimeout(resolve, 1000));
        }
    }
    return false;
}

// 更新链接状态UI
function updateLinkStatusUI(linkItem, status, responseTime = null) {
    const statusDot = linkItem.querySelector('.w-3.h-3.rounded-full');
    const statusText = linkItem.querySelector('.text-sm');

    if (status === 'checking') {
        statusDot.className = 'w-3 h-3 rounded-full bg-yellow-500 mr-2';
        statusText.textContent = '检查中...';
        statusText.className = 'text-sm text-yellow-500 font-medium';
    } else if (status === 'online') {
        statusDot.className = 'w-3 h-3 rounded-full bg-success mr-2';
        const timeText = responseTime ? ` (${responseTime}ms)` : '';
        statusText.textContent = `在线${timeText}`;
        statusText.className = 'text-sm text-success font-medium';
    } else if (status === 'offline') {
        statusDot.className = 'w-3 h-3 rounded-full bg-red-500 mr-2';
        statusText.textContent = '离线';
        statusText.className = 'text-sm text-red-500 font-medium';
    } else if (status === 'timeout') {
        statusDot.className = 'w-3 h-3 rounded-full bg-yellow-500 mr-2';
        statusText.textContent = '超时';
        statusText.className = 'text-sm text-yellow-500 font-medium';
    }
}

// 检测单个链接并更新UI
async function checkAndUpdateSingleLink(site, linkIndex) {
    const linksContainer = document.getElementById(`${site.name.toLowerCase()}-links`);
    const linkItem = linksContainer.querySelector(`.link-item:nth-child(${linkIndex + 1})`);
    const url = site.links[linkIndex].url;

    if (!linkItem) return;

    try {
        // 显示检查中状态
        updateLinkStatusUI(linkItem, 'checking');

        const startTime = Date.now();
        const isOnline = await checkLinkStatusWithRetry(url);
        const responseTime = Date.now() - startTime;

        if (isOnline) {
            site.links[linkIndex].status = 'online';
            site.links[linkIndex].responseTime = responseTime;
            updateLinkStatusUI(linkItem, 'online', responseTime);
        } else {
            site.links[linkIndex].status = 'offline';
            site.links[linkIndex].responseTime = null;
            updateLinkStatusUI(linkItem, 'offline');
        }

        // 重新生成链接列表以应用最新状态
        regenerateSiteLinks(site);

        return isOnline;
    } catch (error) {
        ConsoleStyles.log.error(`检查链接时发生错误: ${url}`, error.message);
        site.links[linkIndex].status = 'timeout';
        site.links[linkIndex].responseTime = null;
        updateLinkStatusUI(linkItem, 'timeout');
        regenerateSiteLinks(site);
        return false;
    }
}

// 更新所有链接状态
async function updateLinkStatuses() {
    ConsoleStyles.group.start('🔄 批量更新链接状态');

    // 显示刷新中状态
    refreshBtn.disabled = true;
    const refreshIcon = document.getElementById('refresh-icon');

    refreshIcon.classList.add('refresh-spinning');

    // 显示加载提示（可选，用于长时间检查）
    if (sites.reduce((total, site) => total + site.links.length, 0) > 5) {
        loadingManager.show();
        loadingManager.updateText('正在检查线路状态', '请稍候，正在测试所有线路的可用性');
    }

    // 更新最后检查时间
    const now = new Date();
    const formattedTime = now.toLocaleString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit'
    });
    lastCheckedEl.textContent = `最后检查: ${formattedTime}`;

    // 统计信息
    let totalLinks = 0;
    let checkedLinks = 0;

    sites.forEach(site => {
        totalLinks += site.links.length;
    });

    // 更新进度显示
    function updateProgress() {
        const progress = Math.round((checkedLinks / totalLinks) * 100);
        // 进度信息现在通过title属性显示
        refreshBtn.title = `刷新状态 - 检查中... ${progress}%`;
    }

    try {
        // 并发检查所有链接，但限制并发数量
        const concurrencyLimit = 3;
        const allChecks = [];

        for (const site of sites) {
            for (let i = 0; i < site.links.length; i++) {
                allChecks.push(async () => {
                    const result = await checkAndUpdateSingleLink(site, i);
                    checkedLinks++;
                    updateProgress();
                    return result;
                });
            }
        }

        // 分批执行检查
        for (let i = 0; i < allChecks.length; i += concurrencyLimit) {
            const batch = allChecks.slice(i, i + concurrencyLimit);
            await Promise.all(batch.map(check => check()));
        }

        showNotification('所有链接状态已更新');
    } catch (error) {
        ConsoleStyles.log.error('更新链接状态时发生错误:', error.message);
        showNotification('更新链接状态时出现错误');
    } finally {
        // 恢复刷新按钮
        refreshBtn.disabled = false;
        const refreshIcon = document.getElementById('refresh-icon');

        refreshIcon.classList.remove('refresh-spinning');
        refreshBtn.title = '刷新状态';

        // 隐藏加载动画（如果正在显示）
        if (loadingManager.isVisible) {
            loadingManager.updateText('检查完成', '所有线路状态已更新');
            setTimeout(() => {
                loadingManager.hide();
            }, 800);
        }

        // 最终更新统计信息
        updateStatistics();
        ConsoleStyles.group.end();
    }
}

// 单独检查链接状态
async function checkSingleLinkStatus(siteName, linkIndex) {
    const site = sites.find(s => s.name.toLowerCase() === siteName);
    if (!site || !site.links[linkIndex]) return;

    const url = site.links[linkIndex].url;
    const linkData = site.links[linkIndex];

    try {
        // 更新UI显示检查中状态
        const linksContainer = document.getElementById(`${siteName}-links`);
        const linkItems = linksContainer.querySelectorAll('.link-item');

        if (linkItems[linkIndex]) {
            updateLinkStatusUI(linkItems[linkIndex], 'checking');
        }

        const startTime = Date.now();
        const isOnline = await checkLinkStatusWithRetry(url);
        const responseTime = Date.now() - startTime;

        if (isOnline) {
            linkData.status = 'online';
            linkData.responseTime = responseTime;
            if (linkItems[linkIndex]) {
                updateLinkStatusUI(linkItems[linkIndex], 'online', responseTime);
            }
        } else {
            linkData.status = 'offline';
            linkData.responseTime = null;
            if (linkItems[linkIndex]) {
                updateLinkStatusUI(linkItems[linkIndex], 'offline');
            }
        }

        // 重新生成站点链接以应用最新状态
        regenerateSiteLinks(site);
        updateStatistics();
        showNotification(`${site.name} 线路状态已更新`);
    } catch (error) {
        ConsoleStyles.log.error(`检查链接时发生错误: ${url}`, error.message);
        linkData.status = 'timeout';
        linkData.responseTime = null;

        const linksContainer = document.getElementById(`${siteName}-links`);
        const linkItems = linksContainer.querySelectorAll('.link-item');
        if (linkItems[linkIndex]) {
            updateLinkStatusUI(linkItems[linkIndex], 'timeout');
        }

        regenerateSiteLinks(site);
        updateStatistics();
        showNotification(`${site.name} 线路检查超时`);
    }
}

// 自动检查功能
function startAutoCheck(intervalMinutes) {
    setInterval(() => {
        ConsoleStyles.log.info('🔄 自动检查链接状态...');
        updateLinkStatuses();
    }, intervalMinutes * 60 * 1000);

    ConsoleStyles.log.success(`⏰ 自动检查已启动，间隔: ${intervalMinutes} 分钟`);
}

// 初始化页面
async function init() {
    try {
        ConsoleStyles.group.start('🚀 页面初始化流程');
        ConsoleStyles.log.init('开始初始化页面...');

        // 显示加载动画
        loadingManager.show();
        loadingManager.updateText('正在初始化页面', '正在准备影视导航内容');

        // 加载站点配置
        const configLoaded = await loadSitesConfig();
        ConsoleStyles.log.config(`配置加载状态: ${configLoaded ? '成功' : '使用备用配置'}`);

        // 模拟加载过程
        setTimeout(() => {
            try {
                // 生成站点卡片
                generateSiteCards();

                // 初始化统计信息
                updateStatistics();

                // 添加刷新按钮事件监听
                if (refreshBtn) {
                    refreshBtn.addEventListener('click', updateLinkStatuses);
                }

                // 启动自动检查（30分钟间隔）
                startAutoCheck(30);

                // 隐藏加载动画
                loadingManager.updateText('加载完成', '欢迎使用免费影视导航');
                setTimeout(() => {
                    loadingManager.hide();
                    ConsoleStyles.log.success('加载动画已隐藏');
                }, 800);

                // 页面加载完成后自动检查一次状态
                setTimeout(updateLinkStatuses, 2000);

                ConsoleStyles.log.success('页面初始化完成 🎉');
                ConsoleStyles.group.end();
            } catch (error) {
                ConsoleStyles.log.error('初始化过程中发生错误:', error.message);
                ConsoleStyles.group.end();
                // 确保加载动画被隐藏
                loadingManager.hide();
            }
        }, 1500); // 模拟加载时间
    } catch (error) {
        ConsoleStyles.log.error('初始化过程中发生严重错误:', error.message);
        ConsoleStyles.group.end();
        // 确保加载动画被隐藏
        loadingManager.hide();
    }
}

// 显示控制台欢迎信息
function showWelcomeBanner() {
    const bannerStyle = 'background: linear-gradient(45deg, #165DFF, #10B981); color: white; padding: 10px 20px; border-radius: 8px; font-size: 16px; font-weight: bold;';
    const subStyle = 'color: #6B7280; font-size: 12px; font-style: italic;';

    console.log('%c🎬 免费影视导航系统', bannerStyle);
    console.log('%c   Version 2.0 | 基于现代Web技术构建', subStyle);
    console.log('%c   开发者工具已激活，您可以查看详细的运行日志', subStyle);
    console.log('');
}

// DOM加载完成后初始化
document.addEventListener('DOMContentLoaded', async function() {
    // 显示欢迎横幅
    showWelcomeBanner();

    // 初始化DOM元素引用
    ConsoleStyles.log.init('初始化DOM元素引用...');
    refreshBtn = document.getElementById('refresh-btn');
    lastCheckedEl = document.querySelector('#last-checked span');
    notification = document.getElementById('notification');
    notificationText = document.getElementById('notification-text');
    sitesContainer = document.getElementById('sites-container');

    // 统计面板元素
    totalLinksEl = document.getElementById('total-links');
    onlineLinksEl = document.getElementById('online-links');
    offlineLinksEl = document.getElementById('offline-links');
    successRateEl = document.getElementById('success-rate');

    // 初始化管理器
    ConsoleStyles.log.init('正在初始化管理器...');
    themeManager = new ThemeManager();
    loadingManager = new LoadingManager();
    ConsoleStyles.log.success('管理器初始化完成');

    // 额外的主题切换调试
    setTimeout(() => {
        const themeToggle = document.getElementById('theme-toggle');
        const themeDropdown = document.getElementById('theme-dropdown');

        ConsoleStyles.log.theme('延迟检查主题元素', {
            themeToggle: !!themeToggle,
            themeDropdown: !!themeDropdown,
            themeToggleVisible: themeToggle ? getComputedStyle(themeToggle).display !== 'none' : false
        });

        // 备用事件监听器已移除，避免重复绑定
    }, 1000);

    // 初始化页面
    await init();
});
