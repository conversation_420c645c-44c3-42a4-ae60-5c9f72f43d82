/* 免费影视导航 - 样式文件 */

/* Tailwind CSS 引入 */
@import url('https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css');

/* 自定义CSS变量 */
:root {
  --primary: #165DFF;
  --success: #10B981;
  --warning: #F59E0B;
  --danger: #EF4444;
  --dark: #1F2937;
}

/* 基础样式 */
.font-inter {
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

.text-primary {
  color: var(--primary);
}

.bg-primary {
  background-color: var(--primary);
}

.border-primary {
  border-color: var(--primary);
}

.text-success {
  color: var(--success);
}

.bg-success {
  background-color: var(--success);
}

.text-dark {
  color: var(--dark);
}

/* 主题切换按钮样式 */
.theme-option.active {
  background-color: var(--primary);
  color: white;
}

/* 刷新按钮旋转动画 */
.refresh-spinning {
  animation: spin 1s linear infinite;
}

/* 脉冲动画 */
.animate-pulse-slow {
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: .5;
  }
}

/* 旋转动画 */
@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

/* 响应式断点优化 */
@media (min-width: 475px) {
  .xs\:block {
    display: block;
  }
}

/* 可爱风格加载动画 */
.loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(5px);
  z-index: 9999;
  display: flex;
  justify-content: center;
  align-items: center;
  transition: opacity 0.3s ease, visibility 0.3s ease;
}

.dark .loading-overlay {
  background: rgba(0, 0, 0, 0.8);
}

.loading-overlay.hidden {
  opacity: 0;
  visibility: hidden;
}

.loading-container {
  text-align: center;
  padding: 2rem;
}

.loading-spinner {
  position: relative;
  width: 80px;
  height: 80px;
  margin: 0 auto 1.5rem;
}

.loading-circle {
  position: absolute;
  width: 100%;
  height: 100%;
  border: 4px solid transparent;
  border-top: 4px solid #165DFF;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

.loading-circle:nth-child(2) {
  border-top-color: #10B981;
  animation-delay: -0.25s;
  transform: scale(0.8);
}

.loading-circle:nth-child(3) {
  border-top-color: #F59E0B;
  animation-delay: -0.5s;
  transform: scale(0.6);
}

.loading-dots {
  display: flex;
  justify-content: center;
  gap: 0.5rem;
  margin-bottom: 1rem;
}

.loading-dot {
  width: 12px;
  height: 12px;
  background: #165DFF;
  border-radius: 50%;
  animation: bounce 1.4s ease-in-out infinite both;
}

.loading-dot:nth-child(1) { animation-delay: -0.32s; }
.loading-dot:nth-child(2) { animation-delay: -0.16s; background: #10B981; }
.loading-dot:nth-child(3) { animation-delay: 0s; background: #F59E0B; }

.loading-text {
  font-size: 1.1rem;
  font-weight: 600;
  color: #165DFF;
  margin-bottom: 0.5rem;
  animation: pulse 2s ease-in-out infinite;
}

.dark .loading-text {
  color: #60A5FA;
}

.loading-subtitle {
  font-size: 0.9rem;
  color: #6B7280;
  animation: fadeInOut 3s ease-in-out infinite;
}

.dark .loading-subtitle {
  color: #9CA3AF;
}

@keyframes bounce {
  0%, 80%, 100% {
    transform: scale(0);
  }
  40% {
    transform: scale(1);
  }
}

@keyframes fadeInOut {
  0%, 100% {
    opacity: 0.5;
  }
  50% {
    opacity: 1;
  }
}

/* 可爱的心跳动画 */
.loading-heart {
  display: inline-block;
  color: #EF4444;
  animation: heartbeat 1.5s ease-in-out infinite;
  margin: 0 0.25rem;
}

@keyframes heartbeat {
  0% {
    transform: scale(1);
  }
  14% {
    transform: scale(1.2);
  }
  28% {
    transform: scale(1);
  }
  42% {
    transform: scale(1.2);
  }
  70% {
    transform: scale(1);
  }
}

/* 通知样式 */
.notification {
  position: fixed;
  top: 20px;
  right: 20px;
  background: white;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  padding: 12px 16px;
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
  z-index: 1000;
  transform: translateX(100%);
  transition: transform 0.3s ease;
}

.notification.show {
  transform: translateX(0);
}

.dark .notification {
  background: #374151;
  border-color: #4b5563;
  color: #f9fafb;
}

/* 链接项悬停效果 */
.link-item:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.dark .link-item:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
}

/* 站点卡片样式 */
.site-card {
  transition: all 0.3s ease;
}

.site-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

.dark .site-card:hover {
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
}

/* 下拉菜单样式 */
.dropdown-menu {
  opacity: 0;
  transform: translateY(-10px);
  transition: all 0.2s ease;
  pointer-events: none;
}

.dropdown-menu.show {
  opacity: 1;
  transform: translateY(0);
  pointer-events: auto;
}

/* 统计卡片样式 */
.stat-card {
  background: linear-gradient(135deg, rgba(22, 93, 255, 0.1), rgba(22, 93, 255, 0.05));
  border: 1px solid rgba(22, 93, 255, 0.2);
}

.dark .stat-card {
  background: linear-gradient(135deg, rgba(22, 93, 255, 0.2), rgba(22, 93, 255, 0.1));
  border-color: rgba(22, 93, 255, 0.3);
}

/* 响应式优化 */
@media (max-width: 640px) {
  .loading-spinner {
    width: 60px;
    height: 60px;
  }
  
  .loading-container {
    padding: 1.5rem;
  }
  
  .notification {
    right: 10px;
    left: 10px;
    transform: translateY(-100%);
  }
  
  .notification.show {
    transform: translateY(0);
  }
}

/* 从index.html中提取的额外样式 */
@layer utilities {
  .card-shadow {
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  }
  .dark .card-shadow {
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.3), 0 4px 6px -2px rgba(0, 0, 0, 0.2);
  }
  .card-hover {
    transition: all 0.3s ease;
  }
  .card-hover:hover {
    transform: translateY(-5px);
    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  }
  .dark .card-hover:hover {
    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.4), 0 10px 10px -5px rgba(0, 0, 0, 0.3);
  }
  .status-pulse {
    animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
  }
  @keyframes pulse {
    0%, 100% {
      opacity: 1;
    }
    50% {
      opacity: 0.7;
    }
  }
  .link-item {
    cursor: pointer;
    user-select: none;
  }
  .link-item:hover {
    transform: translateY(-1px);
  }
  .link-item:active {
    transform: translateY(0);
  }

  /* 主题切换下拉菜单样式 */
  .theme-option.active {
    background-color: #165DFF;
    color: white;
  }
  .theme-option.active:hover {
    background-color: #165DFF;
  }

  /* 下拉菜单显示状态 */
  #theme-dropdown.show {
    opacity: 1 !important;
    visibility: visible !important;
    transform: scale(1) !important;
    pointer-events: auto !important;
  }

  /* 调试样式 - 临时添加边框 */
  #theme-dropdown {
    border: 2px solid transparent;
  }

  #theme-dropdown.show {
    border-color: #165DFF;
  }

  /* 响应式断点优化 */
  @media (min-width: 475px) {
    .xs\:block {
      display: block;
    }
  }
}
